#include "websocket_client.h"
#include <iostream>
#include <chrono>
#include <iomanip>

WebSocketClient::WebSocketClient() : m_connected(false) {
    // 设置日志级别
    m_client.set_access_channels(websocketpp::log::alevel::all);
    m_client.clear_access_channels(websocketpp::log::alevel::frame_payload);
    
    // 初始化 ASIO
    m_client.init_asio();
    
    // 设置事件处理器
    m_client.set_open_handler([this](websocketpp::connection_hdl hdl) {
        this->on_open(hdl);
    });
    
    m_client.set_close_handler([this](websocketpp::connection_hdl hdl) {
        this->on_close(hdl);
    });
    
    m_client.set_message_handler([this](websocketpp::connection_hdl hdl, client::message_ptr msg) {
        this->on_message(hdl, msg);
    });
    
    m_client.set_fail_handler([this](websocketpp::connection_hdl hdl) {
        this->on_fail(hdl);
    });
    
    // 设置 TLS 初始化处理器
    m_client.set_tls_init_handler([this](websocketpp::connection_hdl hdl) {
        return this->on_tls_init(hdl);
    });
}

WebSocketClient::~WebSocketClient() {
    disconnect();
}

bool WebSocketClient::connect(const std::string& uri) {
    try {
        log_info("正在连接到: " + uri);
        
        websocketpp::lib::error_code ec;
        client::connection_ptr con = m_client.get_connection(uri, ec);
        
        if (ec) {
            log_error("连接创建失败: " + ec.message());
            return false;
        }
        
        m_hdl = con->get_handle();
        m_client.connect(con);
        
        // 在单独的线程中运行客户端
        m_thread = std::make_unique<std::thread>([this]() {
            try {
                m_client.run();
            } catch (const std::exception& e) {
                log_error("客户端运行异常: " + std::string(e.what()));
            }
        });
        
        return true;
    } catch (const std::exception& e) {
        log_error("连接异常: " + std::string(e.what()));
        return false;
    }
}

void WebSocketClient::disconnect() {
    if (m_connected) {
        try {
            websocketpp::lib::error_code ec;
            m_client.close(m_hdl, websocketpp::close::status::going_away, "", ec);
            if (ec) {
                log_error("关闭连接失败: " + ec.message());
            }
        } catch (const std::exception& e) {
            log_error("断开连接异常: " + std::string(e.what()));
        }
    }
    
    m_client.stop();
    
    if (m_thread && m_thread->joinable()) {
        m_thread->join();
    }
    
    m_connected = false;
    log_info("连接已断开");
}

bool WebSocketClient::is_connected() const {
    return m_connected;
}

bool WebSocketClient::send_message(const std::string& message) {
    if (!m_connected) {
        log_error("发送消息失败: 未连接");
        return false;
    }

    try {
        websocketpp::lib::error_code ec;
        m_client.send(m_hdl, message, websocketpp::frame::opcode::text, ec);

        if (ec) {
            log_error("发送消息失败: " + ec.message());
            return false;
        }

        log_info("消息已发送: " + message);
        return true;
    } catch (const std::exception& e) {
        log_error("发送消息异常: " + std::string(e.what()));
        return false;
    }
}

// 设置回调函数
void WebSocketClient::set_on_message_callback(std::function<void(const std::string&)> callback) {
    m_on_message = callback;
}

void WebSocketClient::set_on_open_callback(std::function<void()> callback) {
    m_on_open = callback;
}

void WebSocketClient::set_on_close_callback(std::function<void()> callback) {
    m_on_close = callback;
}

void WebSocketClient::set_on_error_callback(std::function<void(const std::string&)> callback) {
    m_on_error = callback;
}

void WebSocketClient::run() {
    try {
        m_client.run();
    } catch (const std::exception& e) {
        log_error("客户端运行异常: " + std::string(e.what()));
    }
}

void WebSocketClient::stop() {
    m_client.stop();
}

// 内部事件处理方法
void WebSocketClient::on_open(websocketpp::connection_hdl hdl) {
    m_connected = true;
    log_info("WebSocket 连接已建立");

    if (m_on_open) {
        m_on_open();
    }
}

void WebSocketClient::on_close(websocketpp::connection_hdl hdl) {
    m_connected = false;
    log_info("WebSocket 连接已关闭");

    if (m_on_close) {
        m_on_close();
    }
}

void WebSocketClient::on_message(websocketpp::connection_hdl hdl, client::message_ptr msg) {
    std::string payload = msg->get_payload();
    log_info("收到消息: " + payload);

    if (m_on_message) {
        m_on_message(payload);
    }
}

void WebSocketClient::on_fail(websocketpp::connection_hdl hdl) {
    m_connected = false;

    client::connection_ptr con = m_client.get_con_from_hdl(hdl);
    std::string error_msg = "连接失败: " + con->get_ec().message();
    log_error(error_msg);

    if (m_on_error) {
        m_on_error(error_msg);
    }
}

// SSL 上下文初始化
context_ptr WebSocketClient::on_tls_init(websocketpp::connection_hdl hdl) {
    context_ptr ctx = websocketpp::lib::make_shared<websocketpp::lib::asio::ssl::context>(
        websocketpp::lib::asio::ssl::context::sslv23);

    try {
        ctx->set_options(websocketpp::lib::asio::ssl::context::default_workarounds |
                        websocketpp::lib::asio::ssl::context::no_sslv2 |
                        websocketpp::lib::asio::ssl::context::no_sslv3 |
                        websocketpp::lib::asio::ssl::context::single_dh_use);
    } catch (std::exception& e) {
        log_error("SSL 上下文初始化失败: " + std::string(e.what()));
    }

    return ctx;
}

// 日志方法
void WebSocketClient::log_info(const std::string& message) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::cout << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] [INFO] " << message << std::endl;
}

void WebSocketClient::log_error(const std::string& message) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::cerr << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] [ERROR] " << message << std::endl;
}
