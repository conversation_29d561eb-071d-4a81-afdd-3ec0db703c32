#pragma once

#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>
#include <websocketpp/common/thread.hpp>
#include <websocketpp/common/memory.hpp>

#include <iostream>
#include <string>
#include <thread>
#include <functional>
#include <memory>

// WebSocket++ 类型定义
typedef websocketpp::client<websocketpp::config::asio_tls_client> client;
typedef websocketpp::lib::shared_ptr<websocketpp::lib::asio::ssl::context> context_ptr;

class WebSocketClient {
public:
    // 构造函数和析构函数
    WebSocketClient();
    ~WebSocketClient();

    // 连接相关方法
    bool connect(const std::string& uri);
    void disconnect();
    bool is_connected() const;

    // 消息发送方法
    bool send_message(const std::string& message);

    // 设置回调函数
    void set_on_message_callback(std::function<void(const std::string&)> callback);
    void set_on_open_callback(std::function<void()> callback);
    void set_on_close_callback(std::function<void()> callback);
    void set_on_error_callback(std::function<void(const std::string&)> callback);

    // 运行客户端（阻塞调用）
    void run();
    
    // 停止客户端
    void stop();

private:
    // WebSocket++ 客户端实例
    client m_client;
    
    // 连接句柄
    websocketpp::connection_hdl m_hdl;
    
    // 连接状态
    bool m_connected;
    
    // 工作线程
    std::unique_ptr<std::thread> m_thread;
    
    // 回调函数
    std::function<void(const std::string&)> m_on_message;
    std::function<void()> m_on_open;
    std::function<void()> m_on_close;
    std::function<void(const std::string&)> m_on_error;

    // 内部事件处理方法
    void on_open(websocketpp::connection_hdl hdl);
    void on_close(websocketpp::connection_hdl hdl);
    void on_message(websocketpp::connection_hdl hdl, client::message_ptr msg);
    void on_fail(websocketpp::connection_hdl hdl);

    // SSL 上下文初始化
    context_ptr on_tls_init(websocketpp::connection_hdl hdl);
    
    // 日志方法
    void log_info(const std::string& message);
    void log_error(const std::string& message);
};
