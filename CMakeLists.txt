cmake_minimum_required(VERSION 3.20)
project(wss-client)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找 vcpkg 工具链
if(DEFINED ENV{VCPKG_ROOT} AND NOT DEFINED CMAKE_TOOLCHAIN_FILE)
    set(CMAKE_TOOLCHAIN_FILE "$ENV{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake"
        CACHE STRING "")
endif()

# 查找依赖包
find_package(Boost REQUIRED COMPONENTS system thread chrono)
find_package(OpenSSL REQUIRED)
find_package(websocketpp CONFIG REQUIRED)

# 添加可执行文件
add_executable(wss-client
    src/main.cpp
    src/websocket_client.cpp
)

# 设置头文件包含目录
target_include_directories(wss-client PRIVATE
    src
    ${Boost_INCLUDE_DIRS}
)

# 链接库
target_link_libraries(wss-client PRIVATE
    ${Boost_LIBRARIES}
    OpenSSL::SSL
    OpenSSL::Crypto
    websocketpp::websocketpp
)

# Windows 特定设置
if(WIN32)
    target_link_libraries(wss-client PRIVATE ws2_32 wsock32)
    target_compile_definitions(wss-client PRIVATE
        WIN32_LEAN_AND_MEAN
        _WIN32_WINNT=0x0601
    )
endif()

# 编译器特定设置
if(MSVC)
    target_compile_options(wss-client PRIVATE /W4)
else()
    target_compile_options(wss-client PRIVATE -Wall -Wextra -Wpedantic)
endif()
