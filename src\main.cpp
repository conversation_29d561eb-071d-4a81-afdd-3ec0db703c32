#include "websocket_client.h"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>

int main() {
    std::cout << "=== WebSocket 客户端启动 ===" << std::endl;
    std::cout << "目标服务器: wss://janus.conf.meetecho.com/ws" << std::endl;
    std::cout << "==============================" << std::endl;

    // 创建 WebSocket 客户端实例
    WebSocketClient client;
    
    // 连接状态标志
    std::atomic<bool> connected(false);
    std::atomic<bool> should_exit(false);

    // 设置连接成功回调
    client.set_on_open_callback([&connected]() {
        std::cout << "\n✅ 连接成功建立！" << std::endl;
        std::cout << "现在可以发送消息了。输入 'quit' 退出程序。" << std::endl;
        connected = true;
    });

    // 设置连接关闭回调
    client.set_on_close_callback([&connected, &should_exit]() {
        std::cout << "\n❌ 连接已关闭" << std::endl;
        connected = false;
        should_exit = true;
    });

    // 设置消息接收回调
    client.set_on_message_callback([](const std::string& message) {
        std::cout << "\n📨 收到服务器消息: " << message << std::endl;
        std::cout << "请输入消息 (或 'quit' 退出): ";
        std::cout.flush();
    });

    // 设置错误回调
    client.set_on_error_callback([&should_exit](const std::string& error) {
        std::cout << "\n❌ 连接错误: " << error << std::endl;
        should_exit = true;
    });

    // 尝试连接到 Janus WebRTC Gateway
    const std::string uri = "wss://janus.conf.meetecho.com/ws";
    
    if (!client.connect(uri)) {
        std::cerr << "❌ 连接失败！" << std::endl;
        return 1;
    }

    std::cout << "⏳ 正在建立连接..." << std::endl;

    // 等待连接建立
    int timeout = 10; // 10秒超时
    while (!connected && !should_exit && timeout > 0) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        timeout--;
    }

    if (!connected && !should_exit) {
        std::cout << "❌ 连接超时！" << std::endl;
        client.disconnect();
        return 1;
    }

    if (should_exit) {
        std::cout << "❌ 连接失败！" << std::endl;
        return 1;
    }

    // 发送初始的 Janus 会话创建消息
    std::string create_session = R"({
        "janus": "create",
        "transaction": "create_session_001"
    })";
    
    std::cout << "\n📤 发送会话创建请求..." << std::endl;
    client.send_message(create_session);

    // 主循环 - 处理用户输入
    std::string input;
    std::cout << "\n请输入消息 (或 'quit' 退出): ";
    
    while (!should_exit && std::getline(std::cin, input)) {
        if (input == "quit" || input == "exit") {
            std::cout << "👋 正在退出..." << std::endl;
            break;
        }
        
        if (input.empty()) {
            std::cout << "请输入消息 (或 'quit' 退出): ";
            continue;
        }
        
        if (connected) {
            // 如果输入的不是 JSON，则包装成简单的 Janus 消息
            std::string message;
            if (input.front() == '{') {
                message = input; // 假设用户输入的是有效的 JSON
            } else {
                // 创建一个简单的 Janus keepalive 消息
                message = R"({
                    "janus": "keepalive",
                    "transaction": "keepalive_)" + std::to_string(std::time(nullptr)) + R"("
                })";
            }
            
            if (client.send_message(message)) {
                std::cout << "✅ 消息已发送" << std::endl;
            } else {
                std::cout << "❌ 消息发送失败" << std::endl;
            }
        } else {
            std::cout << "❌ 连接已断开，无法发送消息" << std::endl;
            break;
        }
        
        std::cout << "请输入消息 (或 'quit' 退出): ";
    }

    // 清理资源
    client.disconnect();
    
    std::cout << "\n👋 程序已退出" << std::endl;
    return 0;
}
