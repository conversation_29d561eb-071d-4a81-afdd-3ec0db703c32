# WebSocket 客户端

基于 WebSocket++ 库的客户端应用程序，用于连接到 Janus WebRTC Gateway 的 WebSocket 接口。

## 功能特性

- ✅ 支持 WSS (WebSocket Secure) 协议
- ✅ 异步连接和消息处理
- ✅ 完整的错误处理机制
- ✅ 连接状态管理和重试
- ✅ JSON 消息格式支持
- ✅ 详细的日志输出

## 系统要求

- **操作系统**: Windows 10/11
- **IDE**: Visual Studio 2022
- **编译器**: MSVC (Visual Studio 2022)
- **包管理**: vcpkg
- **C++ 标准**: C++17

## 依赖库

- **websocketpp**: WebSocket 客户端实现
- **OpenSSL**: WSS 加密连接支持
- **Boost**: system, thread, chrono 组件

## 编译说明

### 1. 安装 vcpkg

如果还没有安装 vcpkg，请按照以下步骤：

```bash
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install
```

### 2. 安装依赖包

```bash
vcpkg install websocketpp:x64-windows
vcpkg install openssl:x64-windows
vcpkg install boost-system:x64-windows
vcpkg install boost-thread:x64-windows
vcpkg install boost-chrono:x64-windows
```

### 3. 配置环境变量

设置 `VCPKG_ROOT` 环境变量指向 vcpkg 安装目录：

```bash
set VCPKG_ROOT=C:\path\to\vcpkg
```

### 4. 编译项目

使用 CMake 生成 Visual Studio 项目：

```bash
mkdir build
cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=%VCPKG_ROOT%/scripts/buildsystems/vcpkg.cmake
```

然后在 Visual Studio 中打开生成的解决方案文件，或使用命令行编译：

```bash
cmake --build . --config Release
```

## 使用说明

### 基本使用

运行编译后的可执行文件：

```bash
.\wss-client.exe
```

程序将自动连接到 `wss://janus.conf.meetecho.com/ws`。

### 交互操作

1. 程序启动后会自动尝试连接到 Janus 服务器
2. 连接成功后，会发送一个会话创建请求
3. 您可以输入消息发送到服务器
4. 输入 `quit` 或 `exit` 退出程序

### 示例输出

```
=== WebSocket 客户端启动 ===
目标服务器: wss://janus.conf.meetecho.com/ws
==============================
[2024-12-XX XX:XX:XX] [INFO] 正在连接到: wss://janus.conf.meetecho.com/ws
⏳ 正在建立连接...
[2024-12-XX XX:XX:XX] [INFO] WebSocket 连接已建立

✅ 连接成功建立！
现在可以发送消息了。输入 'quit' 退出程序。

📤 发送会话创建请求...
[2024-12-XX XX:XX:XX] [INFO] 消息已发送: {"janus": "create", "transaction": "create_session_001"}

📨 收到服务器消息: {"janus":"success","transaction":"create_session_001","data":{"id":123456789}}
请输入消息 (或 'quit' 退出):
```

## 项目结构

```
wss-client/
├── src/
│   ├── main.cpp                 # 主程序入口
│   ├── websocket_client.h       # WebSocket 客户端头文件
│   └── websocket_client.cpp     # WebSocket 客户端实现
├── CMakeLists.txt               # CMake 配置文件
├── vcpkg.json                   # vcpkg 依赖配置
├── requirements.md              # 需求文档
└── README.md                    # 本文档
```

## API 文档

### WebSocketClient 类

主要的 WebSocket 客户端类，提供以下接口：

#### 构造函数
- `WebSocketClient()`: 创建客户端实例

#### 连接管理
- `bool connect(const std::string& uri)`: 连接到指定的 WebSocket 服务器
- `void disconnect()`: 断开连接
- `bool is_connected() const`: 检查连接状态

#### 消息处理
- `bool send_message(const std::string& message)`: 发送消息
- `void set_on_message_callback(std::function<void(const std::string&)> callback)`: 设置消息接收回调

#### 事件回调
- `void set_on_open_callback(std::function<void()> callback)`: 设置连接成功回调
- `void set_on_close_callback(std::function<void()> callback)`: 设置连接关闭回调
- `void set_on_error_callback(std::function<void(const std::string&)> callback)`: 设置错误回调

## 故障排除

### 常见问题

1. **编译错误**: 确保已正确安装所有依赖库
2. **连接失败**: 检查网络连接和防火墙设置
3. **SSL 错误**: 确保 OpenSSL 库正确安装

### 调试模式

编译 Debug 版本以获得更详细的日志输出：

```bash
cmake --build . --config Debug
```

## 许可证

本项目仅用于学习和测试目的。
